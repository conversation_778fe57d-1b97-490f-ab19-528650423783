帮我将 LevelsEconomy.json 转化生成1个CSV文件。
注意原json中是一个物品多个品质都写在1条记录中。
而新的CSV文件中，每个品质是1条记录。

如以下的数据：
    {
      "GearId": 1,
      "Name": "Mage Hat",
      "GearType": 1,
      "Theme": 1,
      "Slot": 1,
      "RarityStats": [
        {
          "Rarity": 0,
          "Stats": [{ "StatType": 5, "StatValue": 0.2, "StatPerLevel": 0.03 }]
        },
        {
          "Rarity": 1,
          "Stats": [
            { "StatType": 2, "StatValue": 1.5, "StatPerLevel": 0.03 },
            { "StatType": 1, "StatValue": 10.0, "StatPerLevel": 2.0 },
            { "StatType": 5, "StatValue": 0.6, "StatPerLevel": 0.04 }
          ]        
        },
      ]
    }
转化为CSV应为2条记录
   GearId\tName\tGearType\tTheme\tSlot\tRarity\tStats
   1_0\tMage Hat\t1\t1\t1\t0\t {StatType5:[0.2,0.03] }
   1_1\tMage Hat\t1\t1\t1\t1\t {StatType2:[1.5,0.03],StatType1:[10.0,2.0],StatType5:[0.6,0.04] }

